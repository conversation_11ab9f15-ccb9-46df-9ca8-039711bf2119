export class BucketPerms {
    bucketPerms() { }
    ;
}
export class Uploader extends BucketPerms {
}
export class SignedUploader extends BucketPerms {
}
export class Downloader extends BucketPerms {
}
export class SignedDownloader extends BucketPerms {
}
export class Attrser extends BucketPerms {
}
export class Lister extends BucketPerms {
}
export class Remover extends Buck<PERSON>Perms {
}
export class PublicUrler extends BucketPerms {
}
//# sourceMappingURL=refs.js.map