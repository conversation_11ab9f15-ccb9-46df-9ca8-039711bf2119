import { Filter, RequestHandler } from '../types';
import { LegacyOptions } from './types';
import type * as http from 'http';
/**
 * @deprecated
 * This function is deprecated and will be removed in a future version.
 *
 * Use {@link createProxyMiddleware} instead.
 */
export declare function legacyCreateProxyMiddleware<TReq = http.IncomingMessage, TRes = http.ServerResponse>(shortHand: string): RequestHandler<TReq, TRes>;
export declare function legacyCreateProxyMiddleware<TReq = http.IncomingMessage, TRes = http.ServerResponse>(legacyOptions: LegacyOptions<TReq, TRes>): RequestHandler<TReq, TRes>;
export declare function legacyCreateProxyMiddleware<TReq = http.IncomingMessage, TRes = http.ServerResponse>(legacyContext: Filter<TReq>, legacyOptions: LegacyOptions<TReq, TRes>): RequestHand<PERSON><TReq, TRes>;
