export interface AppMeta {
    appId: string;
    apiBaseUrl: string;
    environment: EnvironmentMeta;
    build: BuildMeta;
    deploy: DeployMeta;
}
export interface EnvironmentMeta {
    name: string;
    type: EnvironmentType;
    cloud: CloudProvider;
}
export type EnvironmentType = "production" | "development" | "ephemeral" | "test";
export type CloudProvider = "aws" | "gcp" | "azure" | "encore" | "local";
export interface BuildMeta {
    revision: string;
    uncommittedChanges: boolean;
}
export interface DeployMeta {
    id: string;
    hostedServices: Record<string, HostedService>;
}
export interface HostedService {
    name: string;
}
export declare function appMeta(): AppMeta;
