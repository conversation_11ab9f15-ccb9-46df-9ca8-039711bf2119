/* eslint-disable */
import { currentRequest } from "../mod.js";
export { RawRequest, RawResponse } from "../internal/api/node_http.js";
export function api(options, fn) {
    return fn;
}
api.raw = function raw(options, fn) {
    return fn;
};
function streamInOut(options, fn) {
    return fn;
}
function streamIn(options, fn) {
    return fn;
}
function streamOut(options, fn) {
    return fn;
}
api.streamInOut = streamInOut;
api.streamIn = streamIn;
api.streamOut = streamOut;
export class StaticAssets {
    options;
    constructor(options) {
        this.options = options;
    }
}
api.static = function staticAssets(options) {
    return new StaticAssets(options);
};
export class MiddlewareRequest {
    _reqMeta;
    _stream;
    _rawReq;
    _rawResp;
    _data;
    constructor(stream, rawReq, rawResp) {
        this._stream = stream;
        this._rawReq = rawReq;
        this._rawResp = rawResp;
    }
    /**
     * requestMeta is set when the handler is a typed handler or a stream handler.
     * for raw handlers, see rawRequest and rawResponse.
     */
    get requestMeta() {
        return this._reqMeta || (this._reqMeta = currentRequest());
    }
    /**
     * stream is set when the handler is a stream handler.
     */
    get stream() {
        return this._stream;
    }
    /**
     * rawRequest is set when the handler is a raw request handler.
     */
    get rawRequest() {
        return this._rawReq;
    }
    /**
     * rawResponse is set when the handler is a raw request handler.
     */
    get rawResponse() {
        return this._rawResp;
    }
    /**
     * data can be used to pass data from middlewares to the handler.
     * The data will be available via `currentRequest()`
     */
    get data() {
        if (this._data === undefined) {
            this._data = {};
        }
        return this._data;
    }
}
export class ResponseHeader {
    headers;
    constructor() {
        this.headers = {};
    }
    /**
     * set will set a header value for a key, if a previous middleware has
     * already set a value, it will be overridden.
     */
    set(key, value) {
        this.headers[key] = value;
    }
    /**
     * add adds a header value to a key, if a previous middleware has
     * already set a value, they will be appended.
     */
    add(key, value) {
        const prev = this.headers[key];
        if (prev === undefined) {
            this.headers[key] = value;
        }
        else {
            this.headers[key] = [prev, value].flat();
        }
    }
}
export class HandlerResponse {
    /**
     * The payload returned by the handler when the handler is either
     * a typed handler or stream handler.
     */
    payload;
    _headers;
    _status;
    constructor(payload) {
        this.payload = payload;
    }
    /**
     * header can be used by middlewares to set headers to the
     * response. This only works for typed handler. For raw handlers
     * see MiddlewareRequest.rawResponse.
     */
    get header() {
        if (this._headers === undefined) {
            this._headers = new ResponseHeader();
        }
        return this._headers;
    }
    /**
     * Override the http status code for successful requests for typed endpoints.
     */
    set status(s) {
        this._status = s;
    }
    /**
     * __internalToResponse converts a response to the internal representation
     */
    __internalToResponse() {
        return {
            payload: this.payload,
            extraHeaders: this._headers?.headers,
            status: this._status
        };
    }
}
export function middleware(a, b) {
    if (b === undefined) {
        return a;
    }
    else {
        const opts = a;
        // Wrap the middleware function to delegate calls and preserve the original options.
        // The options object is stored separately and made immutable to prevent accidental mutation.
        const mw = (req, next) => {
            return b(req, next);
        };
        mw.options = Object.freeze({ ...opts });
        return mw;
    }
}
export { APIError, ErrCode } from "./error.js";
export { Gateway } from "./gateway.js";
export { IterableSocket, IterableStream, Sink } from "./stream.js";
//# sourceMappingURL=mod.js.map