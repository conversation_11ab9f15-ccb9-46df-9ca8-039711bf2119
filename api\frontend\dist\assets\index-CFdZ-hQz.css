/* Estilos customizados para SweetAlert2 - Padrões organizados */

/* Remover todas as barras de rolagem do SweetAlert2 */
.swal2-container,
.swal2-container *,
.swal2-popup,
.swal2-popup *,
.swal2-html-container,
.swal2-content,
.swal2-modal {
  overflow: hidden !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Webkit browsers (Chrome, Safari, Edge) */
.swal2-container::-webkit-scrollbar,
.swal2-container *::-webkit-scrollbar,
.swal2-popup::-webkit-scrollbar,
.swal2-popup *::-webkit-scrollbar,
.swal2-html-container::-webkit-scrollbar,
.swal2-content::-webkit-scrollbar,
.swal2-modal::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Configurações gerais do popup */
.swal-clean-theme {
  border-radius: 15px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  overflow: hidden !important;
}

/* Título dos alertas */
.swal-clean-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 15px !important;
}

/* Conteúdo dos alertas */
.swal-clean-content {
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #4b5563 !important;
  overflow: hidden !important;
}

/* Remover barras de rolagem de todos os elementos SweetAlert2 */
.swal2-popup {
  overflow: hidden !important;
}

/* Estilos específicos para o modal de resultado de teste */
.test-result-modal .test-result-content {
  max-height: 300px !important;
  overflow-y: auto !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Webkit browsers (Chrome, Safari, Edge) - esconder barra de rolagem */
.test-result-modal .test-result-content::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Garantir que o conteúdo seja rolável mas sem barra visível */
.test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.test-result-content::-webkit-scrollbar {
  display: none !important;
}

/* Sobrescrever estilos de scrollbar do sweetalert2-custom.css para o modal de teste */
.test-result-modal .swal2-html-container::-webkit-scrollbar,
.test-result-modal .test-result-content::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.test-result-modal .swal2-html-container,
.test-result-modal .test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.swal2-html-container {
  overflow: hidden !important;
  max-height: none !important;
}

.swal2-content {
  overflow: hidden !important;
}

.swal2-modal {
  overflow: hidden !important;
}

/* Botão principal (confirmar) */
.swal-clean-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: white !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.swal-clean-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15) !important;
}

.swal-clean-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
}

/* Botão cancelar */
.swal-clean-cancel-button {
  background: #6b7280 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: white !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.swal-clean-cancel-button:hover {
  background: #4b5563 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15) !important;
}

/* Spinner de loading customizado - apenas um círculo */
.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ocultar o spinner padrão do SweetAlert2 */
.swal2-loader {
  display: none !important;
}

/* Inputs customizados para formulários */
.swal2-input {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: border-color 0.2s ease !important;
  background: #ffffff !important;
}

.swal2-input:focus {
  border-color: #667eea !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.swal2-input::placeholder {
  color: #9ca3af !important;
}

/* Validação de erro */
.swal2-validation-message {
  background: #fef2f2 !important;
  color: #dc2626 !important;
  border: 1px solid #fecaca !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
  margin-top: 8px !important;
}

/* Responsividade aprimorada */
@media (max-width: 480px) {
  .swal-clean-theme {
    margin: 8px !important;
    width: calc(100% - 16px) !important;
    max-width: none !important;
    min-height: auto !important;
  }

  .swal-clean-title {
    font-size: 16px !important;
    line-height: 1.4 !important;
    margin-bottom: 12px !important;
  }

  .swal-clean-content {
    font-size: 13px !important;
    line-height: 1.5 !important;
    padding: 0 8px !important;
  }

  .swal-clean-button,
  .swal-clean-cancel-button {
    padding: 8px 16px !important;
    font-size: 12px !important;
    min-width: 80px !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
  }

  .swal2-actions button {
    width: 100% !important;
    margin: 0 !important;
  }
}

@media (max-width: 640px) and (min-width: 481px) {
  .swal-clean-theme {
    margin: 12px !important;
    width: calc(100% - 24px) !important;
  }

  .swal-clean-title {
    font-size: 18px !important;
  }

  .swal-clean-button,
  .swal-clean-cancel-button {
    padding: 10px 20px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 768px) and (min-width: 641px) {
  .swal-clean-theme {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-width: 500px !important;
  }
}

/* Cores específicas para cada tipo de alerta */

/* Sucesso - Verde */
.swal-success .swal-clean-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.swal-success .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3) !important;
}

/* Erro - Vermelho */
.swal-error .swal-clean-button {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.swal-error .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3) !important;
}

/* Informação - Ciano */
.swal-info .swal-clean-button {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

.swal-info .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.3) !important;
}

/* Aviso - Laranja */
.swal-warning .swal-clean-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.swal-warning .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3) !important;
}

/* Confirmação - Azul */
.swal-confirm .swal-clean-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.swal-confirm .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* Estilos para modal de tutoriais */
.tutorials-modal {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.tutorials-modal .swal2-html-container {
  padding: 0 !important;
  margin: 0 !important;
  max-height: 70vh !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar {
  width: 6px !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar-track {
  background: transparent !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5) !important;
  border-radius: 20px !important;
  border: transparent !important;
}

.tutorials-modal .swal2-popup {
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
}

/* ===== RESPONSIVIDADE MOBILE ===== */

/* Configurações gerais para mobile */
@media (max-width: 480px) {
  /* Todos os modais em mobile */
  .swal2-popup {
    width: 95vw !important;
    max-width: 95vw !important;
    max-height: 85vh !important;
    margin: 10px !important;
    padding: 15px !important;
  }

  /* Títulos menores em mobile */
  .swal2-title {
    font-size: 18px !important;
    line-height: 1.3 !important;
    margin-bottom: 15px !important;
  }

  /* Conteúdo com scroll em mobile */
  .swal2-html-container {
    max-height: 60vh !important;
    overflow-y: auto !important;
    padding: 10px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
  }

  /* Botões menores em mobile */
  .swal2-actions {
    flex-direction: column !important;
    gap: 10px !important;
    width: 100% !important;
  }

  .swal2-confirm,
  .swal2-cancel {
    width: 100% !important;
    padding: 12px !important;
    font-size: 16px !important;
    margin: 0 !important;
  }
}

/* Modal de teste específico para mobile */
@media (max-width: 480px) {
  .test-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .test-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 15px !important;
  }
}

/* Modal de tutoriais específico para mobile */
@media (max-width: 480px) {
  .tutorials-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .tutorials-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 10px !important;
  }
}

/* Modal PIX específico para mobile */
@media (max-width: 480px) {
  .pix-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .pix-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 15px !important;
  }
}

/* Estilos para abas do modal de tutoriais */
.tutorials-modal .tab-button {
  transition: all 0.3s ease !important;
  border: none !important;
  outline: none !important;
  font-family: inherit !important;
}

.tutorials-modal .tab-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.tutorials-modal .tab-content {
  animation: fadeIn 0.3s ease-in-out !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos para marcas de TV */
.tutorials-modal .brand-item {
  border-left: 3px solid #059669 !important;
  padding-left: 8px !important;
  margin-bottom: 12px !important;
}

.tutorials-modal .compatibility-badge {
  display: inline-block !important;
  background: #10b981 !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-pan-x: initial;
      --tw-pan-y: initial;
      --tw-pinch-zoom: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-emerald-500: oklch(69.6% .17 162.48);
    --color-emerald-600: oklch(59.6% .145 163.225);
    --color-emerald-700: oklch(50.8% .118 165.612);
    --color-emerald-800: oklch(43.2% .095 166.913);
    --color-cyan-600: oklch(60.9% .126 221.723);
    --color-cyan-700: oklch(52% .105 223.128);
    --color-cyan-800: oklch(45% .085 224.283);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-600: oklch(51.1% .262 276.966);
    --color-indigo-700: oklch(45.7% .24 277.023);
    --color-indigo-800: oklch(39.8% .195 277.366);
    --color-indigo-900: oklch(35.9% .144 278.697);
    --color-purple-300: oklch(82.7% .119 306.383);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-pink-400: oklch(71.8% .202 349.761);
    --color-pink-500: oklch(65.6% .241 354.308);
    --color-slate-900: oklch(20.8% .042 265.755);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-2xl: 1rem;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]) {
    appearance: button;
  }

  ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components;

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .\@container {
    container-type: inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .collapse {
    visibility: collapse;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .not-sr-only {
    clip: auto;
    white-space: normal;
    width: auto;
    height: auto;
    margin: 0;
    padding: 0;
    position: static;
    overflow: visible;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-3 {
    top: calc(var(--spacing) * -3);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-full {
    top: 100%;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-full {
    bottom: 100%;
  }

  .left-1\/2 {
    left: 50%;
  }

  .isolate {
    isolation: isolate;
  }

  .isolation-auto {
    isolation: auto;
  }

  .z-10 {
    z-index: 10;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[100\] {
    z-index: 100;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .container {
    margin-inline: auto;
    padding-inline: 1rem;
  }

  @media (min-width: 40rem) {
    .container {
      padding-inline: 2rem;
    }
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .flow-root {
    display: flow-root;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .inline-grid {
    display: inline-grid;
  }

  .inline-table {
    display: inline-table;
  }

  .list-item {
    display: list-item;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-column {
    display: table-column;
  }

  .table-column-group {
    display: table-column-group;
  }

  .table-footer-group {
    display: table-footer-group;
  }

  .table-header-group {
    display: table-header-group;
  }

  .table-row {
    display: table-row;
  }

  .table-row-group {
    display: table-row-group;
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .h-0 {
    height: calc(var(--spacing) * 0);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-px {
    height: 1px;
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-\[95vh\] {
    max-height: 95vh;
  }

  .max-h-screen {
    max-height: 100vh;
  }

  .min-h-\[60vh\] {
    min-height: 60vh;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .shrink {
    flex-shrink: 1;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-none {
    translate: none;
  }

  .scale-3d {
    scale: var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z);
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .animate-ping {
    animation: var(--animate-ping);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .cursor-default {
    cursor: default;
  }

  .touch-pinch-zoom {
    --tw-pinch-zoom: pinch-zoom;
    touch-action: var(--tw-pan-x, ) var(--tw-pan-y, ) var(--tw-pinch-zoom, );
  }

  .touch-manipulation {
    touch-action: manipulation;
  }

  .resize {
    resize: both;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-reverse > :not(:last-child)) {
    --tw-space-y-reverse: 1;
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-reverse > :not(:last-child)) {
    --tw-space-x-reverse: 1;
  }

  :where(.divide-x > :not(:last-child)) {
    --tw-divide-x-reverse: 0;
    border-inline-style: var(--tw-border-style);
    border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
    border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-y-reverse > :not(:last-child)) {
    --tw-divide-y-reverse: 1;
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-s {
    border-start-start-radius: .25rem;
    border-end-start-radius: .25rem;
  }

  .rounded-ss {
    border-start-start-radius: .25rem;
  }

  .rounded-e {
    border-start-end-radius: .25rem;
    border-end-end-radius: .25rem;
  }

  .rounded-se {
    border-start-end-radius: .25rem;
  }

  .rounded-ee {
    border-end-end-radius: .25rem;
  }

  .rounded-es {
    border-end-start-radius: .25rem;
  }

  .rounded-t {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
  }

  .rounded-l {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
  }

  .rounded-tl {
    border-top-left-radius: .25rem;
  }

  .rounded-r {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
  }

  .rounded-tr {
    border-top-right-radius: .25rem;
  }

  .rounded-b {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
  }

  .rounded-br {
    border-bottom-right-radius: .25rem;
  }

  .rounded-bl {
    border-bottom-left-radius: .25rem;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-x {
    border-inline-style: var(--tw-border-style);
    border-inline-width: 1px;
  }

  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }

  .border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
  }

  .border-e {
    border-inline-end-style: var(--tw-border-style);
    border-inline-end-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-4 {
    border-top-style: var(--tw-border-style);
    border-top-width: 4px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-r-4 {
    border-right-style: var(--tw-border-style);
    border-right-width: 4px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-blue-400 {
    border-color: var(--color-blue-400);
  }

  .border-destructive {
    border-color: var(--destructive);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-green-400 {
    border-color: var(--color-green-400);
  }

  .border-green-500\/20 {
    border-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/20 {
      border-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .border-input {
    border-color: var(--input);
  }

  .border-purple-400 {
    border-color: var(--color-purple-400);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-white\/10 {
    border-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-t-gray-800 {
    border-top-color: var(--color-gray-800);
  }

  .bg-\[\#4dc247\] {
    background-color: #4dc247;
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-black\/20 {
    background-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/20 {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .bg-black\/40 {
    background-color: #0006;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/40 {
      background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-500\/20 {
    background-color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/20 {
      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }

  .bg-green-500\/10 {
    background-color: #00c7581a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/10 {
      background-color: color-mix(in oklab, var(--color-green-500) 10%, transparent);
    }
  }

  .bg-green-500\/20 {
    background-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/20 {
      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }

  .bg-purple-500\/20 {
    background-color: #ac4bff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/20 {
      background-color: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/5 {
    background-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/5 {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .bg-white\/10 {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-cyan-600 {
    --tw-gradient-from: var(--color-cyan-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-emerald-600 {
    --tw-gradient-from: var(--color-emerald-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-400 {
    --tw-gradient-from: var(--color-green-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-500 {
    --tw-gradient-from: var(--color-green-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-600 {
    --tw-gradient-from: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-600 {
    --tw-gradient-from: var(--color-indigo-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-600 {
    --tw-gradient-from: var(--color-orange-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-400 {
    --tw-gradient-from: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-500 {
    --tw-gradient-from: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-600 {
    --tw-gradient-from: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-900 {
    --tw-gradient-from: var(--color-purple-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-600 {
    --tw-gradient-from: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-slate-900 {
    --tw-gradient-from: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-yellow-600 {
    --tw-gradient-from: var(--color-yellow-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-blue-900 {
    --tw-gradient-via: var(--color-blue-900);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-purple-900 {
    --tw-gradient-via: var(--color-purple-900);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-700 {
    --tw-gradient-to: var(--color-blue-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-700 {
    --tw-gradient-to: var(--color-cyan-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-500 {
    --tw-gradient-to: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-600 {
    --tw-gradient-to: var(--color-emerald-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-700 {
    --tw-gradient-to: var(--color-emerald-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-700 {
    --tw-gradient-to: var(--color-green-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-700 {
    --tw-gradient-to: var(--color-indigo-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-900 {
    --tw-gradient-to: var(--color-indigo-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-orange-600 {
    --tw-gradient-to: var(--color-orange-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-orange-700 {
    --tw-gradient-to: var(--color-orange-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-pink-400 {
    --tw-gradient-to: var(--color-pink-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-pink-500 {
    --tw-gradient-to: var(--color-pink-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-700 {
    --tw-gradient-to: var(--color-purple-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-red-700 {
    --tw-gradient-to: var(--color-red-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-slate-900 {
    --tw-gradient-to: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
  }

  .bg-repeat {
    background-repeat: repeat;
  }

  .mask-no-clip {
    -webkit-mask-clip: no-clip;
    mask-clip: no-clip;
  }

  .mask-repeat {
    -webkit-mask-repeat: repeat;
    mask-repeat: repeat;
  }

  .fill-white {
    fill: var(--color-white);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pr-6 {
    padding-right: calc(var(--spacing) * 6);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .text-center {
    text-align: center;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .text-wrap {
    text-wrap: wrap;
  }

  .text-clip {
    text-overflow: clip;
  }

  .text-ellipsis {
    text-overflow: ellipsis;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-blue-300 {
    color: var(--color-blue-300);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-foreground, .text-foreground\/50 {
    color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/50 {
      color: color-mix(in oklab, var(--foreground) 50%, transparent);
    }
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-300 {
    color: var(--color-green-300);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-purple-300 {
    color: var(--color-purple-300);
  }

  .text-purple-400 {
    color: var(--color-purple-400);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-transparent {
    color: #0000;
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/80 {
    color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/80 {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .capitalize {
    text-transform: capitalize;
  }

  .lowercase {
    text-transform: lowercase;
  }

  .normal-case {
    text-transform: none;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .not-italic {
    font-style: normal;
  }

  .diagonal-fractions {
    --tw-numeric-fraction: diagonal-fractions;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .lining-nums {
    --tw-numeric-figure: lining-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .oldstyle-nums {
    --tw-numeric-figure: oldstyle-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .proportional-nums {
    --tw-numeric-spacing: proportional-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .slashed-zero {
    --tw-slashed-zero: slashed-zero;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .stacked-fractions {
    --tw-numeric-fraction: stacked-fractions;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .normal-nums {
    font-variant-numeric: normal;
  }

  .line-through {
    text-decoration-line: line-through;
  }

  .no-underline {
    text-decoration-line: none;
  }

  .overline {
    text-decoration-line: overline;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-90 {
    opacity: .9;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .inset-ring {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-purple-400 {
    --tw-ring-color: var(--color-purple-400);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #0000001a)) drop-shadow(0 1px 1px var(--tw-drop-shadow-color, #0000000f));
    --tw-drop-shadow: drop-shadow(0 1px 2px #0000001a) drop-shadow(0 1px 1px #0000000f);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-grayscale {
    --tw-backdrop-grayscale: grayscale(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-invert {
    --tw-backdrop-invert: invert(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-sepia {
    --tw-backdrop-sepia: sepia(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  :where(.divide-x-reverse > :not(:last-child)) {
    --tw-divide-x-reverse: 1;
  }

  .ring-inset {
    --tw-ring-inset: inset;
  }

  .running {
    animation-play-state: running;
  }

  .zoom-in {
    --tw-enter-scale: 0;
  }

  .zoom-out {
    --tw-exit-scale: 0;
  }

  @media (hover: hover) {
    .group-hover\:opacity-20:is(:where(.group):hover *) {
      opacity: .2;
    }

    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-\[\.destructive\]\:border-muted\/40:is(:where(.group).destructive *) {
    border-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .group-\[\.destructive\]\:border-muted\/40:is(:where(.group).destructive *) {
      border-color: color-mix(in oklab, var(--muted) 40%, transparent);
    }
  }

  .group-\[\.destructive\]\:text-red-300:is(:where(.group).destructive *) {
    color: var(--color-red-300);
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .selection\:bg-primary ::selection {
    background-color: var(--primary);
  }

  .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection {
    color: var(--primary-foreground);
  }

  .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }

    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }

    .hover\:animate-none:hover {
      animation: none;
    }

    .hover\:border-purple-400\/50:hover {
      border-color: #c07eff80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-purple-400\/50:hover {
        border-color: color-mix(in oklab, var(--color-purple-400) 50%, transparent);
      }
    }

    .hover\:bg-\[\#45b83d\]:hover {
      background-color: #45b83d;
    }

    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }

    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }

    .hover\:bg-gray-600:hover {
      background-color: var(--color-gray-600);
    }

    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }

    .hover\:bg-purple-700:hover {
      background-color: var(--color-purple-700);
    }

    .hover\:bg-secondary:hover, .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }

    .hover\:from-blue-700:hover {
      --tw-gradient-from: var(--color-blue-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-cyan-700:hover {
      --tw-gradient-from: var(--color-cyan-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-emerald-700:hover {
      --tw-gradient-from: var(--color-emerald-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-green-600:hover {
      --tw-gradient-from: var(--color-green-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-green-700:hover {
      --tw-gradient-from: var(--color-green-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-indigo-700:hover {
      --tw-gradient-from: var(--color-indigo-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-orange-700:hover {
      --tw-gradient-from: var(--color-orange-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-purple-700:hover {
      --tw-gradient-from: var(--color-purple-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-red-700:hover {
      --tw-gradient-from: var(--color-red-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:from-yellow-700:hover {
      --tw-gradient-from: var(--color-yellow-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-blue-700:hover {
      --tw-gradient-to: var(--color-blue-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-blue-800:hover {
      --tw-gradient-to: var(--color-blue-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-cyan-800:hover {
      --tw-gradient-to: var(--color-cyan-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-emerald-700:hover {
      --tw-gradient-to: var(--color-emerald-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-emerald-800:hover {
      --tw-gradient-to: var(--color-emerald-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-green-800:hover {
      --tw-gradient-to: var(--color-green-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-indigo-800:hover {
      --tw-gradient-to: var(--color-indigo-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-orange-700:hover {
      --tw-gradient-to: var(--color-orange-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-orange-800:hover {
      --tw-gradient-to: var(--color-orange-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-purple-800:hover {
      --tw-gradient-to: var(--color-purple-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:to-red-800:hover {
      --tw-gradient-to: var(--color-red-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }

    .hover\:text-foreground:hover {
      color: var(--foreground);
    }

    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }

    .hover\:text-purple-400:hover {
      color: var(--color-purple-400);
    }

    .hover\:underline:hover {
      text-decoration-line: underline;
    }

    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .group-\[\.destructive\]\:hover\:border-destructive\/30:is(:where(.group).destructive *):hover {
      border-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-\[\.destructive\]\:hover\:border-destructive\/30:is(:where(.group).destructive *):hover {
        border-color: color-mix(in oklab, var(--destructive) 30%, transparent);
      }
    }

    .group-\[\.destructive\]\:hover\:bg-destructive:is(:where(.group).destructive *):hover {
      background-color: var(--destructive);
    }

    .group-\[\.destructive\]\:hover\:text-red-50:is(:where(.group).destructive *):hover {
      color: var(--color-red-50);
    }
  }

  .focus\:border-purple-500:focus {
    border-color: var(--color-purple-500);
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:opacity-100:focus {
    opacity: 1;
  }

  .focus\:ring-1:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-purple-500:focus {
    --tw-ring-color: var(--color-purple-500);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .group-\[\.destructive\]\:focus\:ring-destructive:is(:where(.group).destructive *):focus {
    --tw-ring-color: var(--destructive);
  }

  .group-\[\.destructive\]\:focus\:ring-red-400:is(:where(.group).destructive *):focus {
    --tw-ring-color: var(--color-red-400);
  }

  .group-\[\.destructive\]\:focus\:ring-offset-red-600:is(:where(.group).destructive *):focus {
    --tw-ring-offset-color: var(--color-red-600);
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: var(--ring);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
    height: 1px;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
    width: 1px;
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:fade-out-80[data-state="closed"] {
    --tw-exit-opacity: .8;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"] {
    --tw-exit-translate-x: calc(1 * 100%);
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .data-\[state\=open\]\:slide-in-from-top-full[data-state="open"] {
    --tw-enter-translate-y: calc(1 * -100%);
  }

  .data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
    --tw-translate-x: var(--radix-toast-swipe-end-x);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=end\]\:animate-out[data-swipe="end"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
    --tw-translate-x: var(--radix-toast-swipe-move-x);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
    transition-property: none;
  }

  @media (min-width: 40rem) {
    .sm\:top-4 {
      top: calc(var(--spacing) * 4);
    }

    .sm\:top-auto {
      top: auto;
    }

    .sm\:right-0 {
      right: calc(var(--spacing) * 0);
    }

    .sm\:right-4 {
      right: calc(var(--spacing) * 4);
    }

    .sm\:right-6 {
      right: calc(var(--spacing) * 6);
    }

    .sm\:bottom-0 {
      bottom: calc(var(--spacing) * 0);
    }

    .sm\:bottom-6 {
      bottom: calc(var(--spacing) * 6);
    }

    .sm\:mx-6 {
      margin-inline: calc(var(--spacing) * 6);
    }

    .sm\:mr-2 {
      margin-right: calc(var(--spacing) * 2);
    }

    .sm\:mb-2 {
      margin-bottom: calc(var(--spacing) * 2);
    }

    .sm\:mb-3 {
      margin-bottom: calc(var(--spacing) * 3);
    }

    .sm\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }

    .sm\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }

    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }

    .sm\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }

    .sm\:block {
      display: block;
    }

    .sm\:h-4 {
      height: calc(var(--spacing) * 4);
    }

    .sm\:h-5 {
      height: calc(var(--spacing) * 5);
    }

    .sm\:h-6 {
      height: calc(var(--spacing) * 6);
    }

    .sm\:h-7 {
      height: calc(var(--spacing) * 7);
    }

    .sm\:h-14 {
      height: calc(var(--spacing) * 14);
    }

    .sm\:w-4 {
      width: calc(var(--spacing) * 4);
    }

    .sm\:w-5 {
      width: calc(var(--spacing) * 5);
    }

    .sm\:w-6 {
      width: calc(var(--spacing) * 6);
    }

    .sm\:w-7 {
      width: calc(var(--spacing) * 7);
    }

    .sm\:w-14 {
      width: calc(var(--spacing) * 14);
    }

    .sm\:max-w-md {
      max-width: var(--container-md);
    }

    .sm\:grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .sm\:flex-col {
      flex-direction: column;
    }

    .sm\:flex-row {
      flex-direction: row;
    }

    .sm\:gap-3 {
      gap: calc(var(--spacing) * 3);
    }

    .sm\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }

    :where(.sm\:space-y-0 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.sm\:space-y-2 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.sm\:space-y-4 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }

    :where(.sm\:space-x-3 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }

    .sm\:rounded-2xl {
      border-radius: var(--radius-2xl);
    }

    .sm\:p-2 {
      padding: calc(var(--spacing) * 2);
    }

    .sm\:p-4 {
      padding: calc(var(--spacing) * 4);
    }

    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }

    .sm\:px-3 {
      padding-inline: calc(var(--spacing) * 3);
    }

    .sm\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }

    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }

    .sm\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }

    .sm\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }

    .sm\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }

    .sm\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }

    .sm\:pb-4 {
      padding-bottom: calc(var(--spacing) * 4);
    }

    .sm\:pb-6 {
      padding-bottom: calc(var(--spacing) * 6);
    }

    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }

    .sm\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }

    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }

    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }

    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }

    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }

    .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"] {
      --tw-enter-translate-y: calc(1 * 100%);
    }
  }

  @media (min-width: 48rem) {
    .md\:-top-4 {
      top: calc(var(--spacing) * -4);
    }

    .md\:mt-4 {
      margin-top: calc(var(--spacing) * 4);
    }

    .md\:mr-2 {
      margin-right: calc(var(--spacing) * 2);
    }

    .md\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }

    .md\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }

    .md\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }

    .md\:mb-10 {
      margin-bottom: calc(var(--spacing) * 10);
    }

    .md\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }

    .md\:mb-16 {
      margin-bottom: calc(var(--spacing) * 16);
    }

    .md\:h-6 {
      height: calc(var(--spacing) * 6);
    }

    .md\:h-8 {
      height: calc(var(--spacing) * 8);
    }

    .md\:h-12 {
      height: calc(var(--spacing) * 12);
    }

    .md\:h-16 {
      height: calc(var(--spacing) * 16);
    }

    .md\:w-6 {
      width: calc(var(--spacing) * 6);
    }

    .md\:w-8 {
      width: calc(var(--spacing) * 8);
    }

    .md\:w-12 {
      width: calc(var(--spacing) * 12);
    }

    .md\:w-16 {
      width: calc(var(--spacing) * 16);
    }

    .md\:max-w-\[420px\] {
      max-width: 420px;
    }

    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }

    .md\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }

    :where(.md\:space-y-3 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }

    .md\:p-3 {
      padding: calc(var(--spacing) * 3);
    }

    .md\:p-4 {
      padding: calc(var(--spacing) * 4);
    }

    .md\:p-6 {
      padding: calc(var(--spacing) * 6);
    }

    .md\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }

    .md\:py-2\.5 {
      padding-block: calc(var(--spacing) * 2.5);
    }

    .md\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }

    .md\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }

    .md\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }

    .md\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }

    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }

    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }

    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }

    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }

    .md\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }

    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }

    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .lg\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }

    .lg\:py-20 {
      padding-block: calc(var(--spacing) * 20);
    }

    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }

    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }

    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }

    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: var(--muted-foreground);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }

    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
    border-color: var(--input);
  }

  .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: var(--foreground);
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .\[\&\+div\]\:text-xs + div {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--accent);
    }

    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }

    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }

    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
      }
    }

    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

.swal-dark-theme {
  background: #1e293b !important;
  border: 1px solid #ffffff1a !important;
  border-radius: .75rem !important;
}

.swal-wide {
  max-width: 90vw !important;
  max-height: 90vh !important;
}

@media (max-width: 480px) {
  .swal-wide {
    max-width: 98vw !important;
    max-height: 90vh !important;
    margin: 4px !important;
  }

  .swal-dark-theme {
    border-radius: 12px !important;
    width: calc(100% - 8px) !important;
    margin: 4px !important;
  }

  .swal-title {
    padding: 8px !important;
    font-size: 1.125rem !important;
    line-height: 1.3 !important;
  }

  .swal-text {
    font-size: .8125rem !important;
    line-height: 1.4 !important;
  }

  .swal-html-container {
    max-height: 65vh !important;
    padding: 4px !important;
    font-size: .8125rem !important;
    overflow-y: auto !important;
  }

  .swal-button {
    border-radius: 8px !important;
    width: 100% !important;
    margin: 4px 0 !important;
    padding: .625rem 1rem !important;
    font-size: .8125rem !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
    padding: 8px !important;
  }

  .swal-html-container img {
    max-width: min(160px, 70vw) !important;
    max-height: min(160px, 70vw) !important;
  }

  .swal-html-container textarea {
    height: 60px !important;
    padding: 8px !important;
    font-size: .75rem !important;
  }
}

@media (max-width: 640px) and (min-width: 481px) {
  .swal-dark-theme {
    width: calc(100% - 16px) !important;
    margin: 8px !important;
  }

  .swal-title {
    font-size: 1.25rem !important;
  }

  .swal-button {
    padding: .75rem 1.25rem !important;
    font-size: .875rem !important;
  }

  .swal-html-container {
    max-height: 70vh !important;
    padding: 8px !important;
  }

  .swal-html-container img {
    max-width: min(180px, 75vw) !important;
    max-height: min(180px, 75vw) !important;
  }
}

@media (max-width: 768px) and (min-width: 641px) {
  .swal-dark-theme {
    width: calc(100% - 32px) !important;
    max-width: 500px !important;
    margin: 16px !important;
  }
}

.swal-title {
  color: #fff !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
}

.swal-text {
  color: #d1d5db !important;
  font-size: 1rem !important;
}

.swal-html-container {
  color: #d1d5db !important;
  max-height: 60vh !important;
  font-size: 1rem !important;
  overflow-y: auto !important;
}

.swal-html-container pre {
  color: #e2e8f0 !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  background: #0f172a !important;
  border: 1px solid #ffffff1a !important;
  border-radius: .5rem !important;
  margin: .5rem 0 !important;
  padding: 1rem !important;
  font-family: Courier New, monospace !important;
  font-size: .875rem !important;
  line-height: 1.5 !important;
  overflow-x: auto !important;
}

.swal-html-container .emoji {
  margin-right: .5rem !important;
  font-size: 1.2em !important;
}

.swal-html-container .highlight {
  color: #c084fc !important;
  background: #a855f733 !important;
  border-radius: .25rem !important;
  padding: .125rem .25rem !important;
  font-weight: 600 !important;
}

.swal-html-container .section-title {
  color: #a855f7 !important;
  border-bottom: 1px solid #a855f74d !important;
  margin: 1rem 0 .5rem !important;
  padding-bottom: .25rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
}

.swal-html-container .info-line {
  margin: .25rem 0 !important;
  padding: .25rem 0 !important;
}

.swal-html-container .link {
  color: #60a5fa !important;
  word-break: break-all !important;
  text-decoration: underline !important;
}

.swal-html-container .link:hover {
  color: #93c5fd !important;
}

.swal-footer {
  background: #0f172a80 !important;
  border-top: 1px solid #ffffff1a !important;
}

.swal-button {
  color: #fff !important;
  background: linear-gradient(135deg, #a855f7, #ec4899) !important;
  border: none !important;
  border-radius: .5rem !important;
  margin: 0 .25rem !important;
  padding: .75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all .3s !important;
}

.swal-button:hover {
  background: linear-gradient(135deg, #9333ea, #db2777) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px #a855f766 !important;
}

.swal-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px #a855f780 !important;
}

.swal-button.swal-button--cancel {
  background: #64748b !important;
}

.swal-button.swal-button--cancel:hover {
  background: #475569 !important;
  box-shadow: 0 4px 12px #64748b66 !important;
}

.swal-input {
  color: #e2e8f0 !important;
  background: #0f172a !important;
  border: 1px solid #ffffff1a !important;
  border-radius: .5rem !important;
  padding: .75rem !important;
  font-size: 1rem !important;
}

.swal-input:focus {
  border-color: #a855f7 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px #a855f733 !important;
}

.swal-input::placeholder {
  color: #64748b !important;
}

.swal-select {
  color: #e2e8f0 !important;
  background: #0f172a !important;
  border: 1px solid #ffffff1a !important;
  border-radius: .5rem !important;
  padding: .75rem !important;
  font-size: 1rem !important;
}

.swal-select:focus {
  border-color: #a855f7 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px #a855f733 !important;
}

.swal-select option {
  color: #e2e8f0 !important;
  background: #0f172a !important;
}

.swal-textarea {
  color: #e2e8f0 !important;
  resize: vertical !important;
  background: #0f172a !important;
  border: 1px solid #ffffff1a !important;
  border-radius: .5rem !important;
  min-height: 100px !important;
  padding: .75rem !important;
  font-size: 1rem !important;
}

.swal-textarea:focus {
  border-color: #a855f7 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px #a855f733 !important;
}

.swal-textarea::placeholder {
  color: #64748b !important;
}

.swal-loading {
  border-color: #a855f7 !important;
}

.swal-loading:after {
  border-color: #a855f7 #0000 !important;
}

.swal-icon {
  border-color: #a855f7 !important;
}

.swal-icon.swal-icon--success {
  border-color: #10b981 !important;
}

.swal-icon.swal-icon--error {
  border-color: #ef4444 !important;
}

.swal-icon.swal-icon--warning {
  border-color: #f59e0b !important;
}

.swal-icon.swal-icon--info {
  border-color: #3b82f6 !important;
}

.swal-icon.swal-icon--question {
  border-color: #8b5cf6 !important;
}

.swal-html-container::-webkit-scrollbar {
  width: 8px !important;
}

.swal-html-container::-webkit-scrollbar-track {
  background: #0f172a80 !important;
  border-radius: 4px !important;
}

.swal-html-container::-webkit-scrollbar-thumb {
  background: #a855f780 !important;
  border-radius: 4px !important;
}

.swal-html-container::-webkit-scrollbar-thumb:hover {
  background: #a855f7b3 !important;
}

.swal2-show {
  animation: .3s ease-out swal2-show !important;
}

.swal2-hide {
  animation: .15s ease-in swal2-hide !important;
}

@keyframes swal2-show {
  0% {
    opacity: 0 !important;
    transform: scale(.7) !important;
  }

  45% {
    opacity: 1 !important;
    transform: scale(1.05) !important;
  }

  80% {
    transform: scale(.95) !important;
  }

  100% {
    opacity: 1 !important;
    transform: scale(1) !important;
  }
}

@keyframes swal2-hide {
  0% {
    opacity: 1 !important;
    transform: scale(1) !important;
  }

  100% {
    opacity: 0 !important;
    transform: scale(.5) !important;
  }
}

@media (max-width: 479px) {
  .container {
    padding-left: .5rem !important;
    padding-right: .5rem !important;
  }

  .card {
    border-radius: .5rem !important;
    margin: .25rem !important;
  }

  button {
    min-height: 44px !important;
    font-size: .875rem !important;
  }

  input, textarea, select {
    min-height: 44px !important;
    padding: .75rem !important;
    font-size: 16px !important;
  }

  h1 {
    font-size: 1.5rem !important;
  }

  h2 {
    font-size: 1.25rem !important;
  }

  h3 {
    font-size: 1.125rem !important;
  }

  h4 {
    font-size: 1rem !important;
  }

  .grid {
    gap: .5rem !important;
  }

  .swal2-popup {
    width: calc(100vw - .5rem) !important;
    max-width: none !important;
    margin: .25rem !important;
  }
}

@media (min-width: 480px) and (max-width: 767px) {
  .container {
    padding-left: .75rem !important;
    padding-right: .75rem !important;
  }

  .grid {
    gap: .75rem !important;
  }

  input, textarea, select {
    font-size: 15px !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .grid {
    gap: 1rem !important;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }

  .grid {
    gap: 1.5rem !important;
  }
}

.text-responsive {
  font-size: clamp(.875rem, 2.5vw, 1rem) !important;
}

.spacing-responsive {
  padding: clamp(.5rem, 2vw, 1rem) !important;
}

.gap-responsive {
  gap: clamp(.5rem, 2vw, 1rem) !important;
}

.gap-responsive-lg {
  gap: clamp(1rem, 3vw, 1.5rem) !important;
}

.flex-responsive {
  flex-direction: column !important;
  gap: clamp(.5rem, 2vw, 1rem) !important;
  display: flex !important;
}

@media (min-width: 640px) {
  .flex-responsive {
    flex-direction: row !important;
    align-items: center !important;
  }
}

.grid-responsive-auto {
  grid-template-columns: 1fr !important;
  gap: clamp(.5rem, 2vw, 1rem) !important;
  display: grid !important;
}

@media (min-width: 640px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  }
}

@media (min-width: 1024px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  }
}

.img-responsive-contain {
  object-fit: contain !important;
  width: 100% !important;
  max-width: 100% !important;
  height: auto !important;
}

.img-responsive-cover {
  object-fit: cover !important;
  width: 100% !important;
  max-width: 100% !important;
  height: auto !important;
}

.hidden-mobile {
  display: none !important;
}

@media (min-width: 640px) {
  .hidden-mobile {
    display: block !important;
  }
}

.visible-mobile {
  display: block !important;
}

@media (min-width: 640px) {
  .visible-mobile {
    display: none !important;
  }
}

@media (hover: none) and (pointer: coarse) {
  button, .btn, [role="button"] {
    min-width: 44px !important;
    min-height: 44px !important;
  }

  input, textarea, select {
    min-height: 44px !important;
  }

  a, button, input, select, textarea {
    padding: .75rem !important;
  }
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

@media (orientation: landscape) and (max-height: 500px) {
  .swal2-popup {
    max-height: 90vh !important;
    overflow-y: auto !important;
  }

  .container {
    padding-top: .5rem !important;
    padding-bottom: .5rem !important;
  }
}

@media print {
  .no-print {
    display: none !important;
  }

  .container {
    max-width: none !important;
    padding: 0 !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }
}

@media (prefers-color-scheme: dark) {
  .auto-dark {
    color: #fff !important;
    background-color: #1a1a1a !important;
  }

  .auto-dark input, .auto-dark textarea, .auto-dark select {
    color: #fff !important;
    background-color: #2a2a2a !important;
    border-color: #444 !important;
  }
}

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.text-responsive-xs {
  font-size: clamp(.75rem, 2vw, .875rem);
}

.text-responsive-sm {
  font-size: clamp(.875rem, 2.5vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 3vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 3.5vw, 1.25rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 5vw, 2rem);
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 6vw, 2.5rem);
}

.text-responsive-4xl {
  font-size: clamp(2.25rem, 7vw, 3rem);
}

.spacing-responsive-sm {
  padding: clamp(.5rem, 2vw, 1rem);
}

.spacing-responsive-md {
  padding: clamp(1rem, 3vw, 1.5rem);
}

.spacing-responsive-lg {
  padding: clamp(1.5rem, 4vw, 2rem);
}

.spacing-responsive-xl {
  padding: clamp(2rem, 5vw, 3rem);
}

.card-responsive {
  border-radius: clamp(.5rem, 2vw, 1rem);
  padding: clamp(1rem, 4vw, 2rem);
}

.btn-responsive {
  border-radius: clamp(.375rem, 1.5vw, .5rem);
  padding: clamp(.5rem, 2vw, .75rem) clamp(1rem, 4vw, 1.5rem);
  font-size: clamp(.875rem, 3vw, 1rem);
}

.btn-responsive-lg {
  border-radius: clamp(.5rem, 2vw, .75rem);
  padding: clamp(.75rem, 3vw, 1rem) clamp(1.5rem, 5vw, 2rem);
  font-size: clamp(1rem, 3.5vw, 1.125rem);
}

.input-responsive {
  border-radius: clamp(.375rem, 1.5vw, .5rem);
  padding: clamp(.5rem, 2vw, .75rem) clamp(.75rem, 3vw, 1rem);
  font-size: clamp(.875rem, 3vw, 1rem);
}

.grid-responsive-1-2 {
  grid-template-columns: 1fr;
  gap: clamp(1rem, 4vw, 2rem);
  display: grid;
}

@media (min-width: 768px) {
  .grid-responsive-1-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.grid-responsive-1-2-3 {
  grid-template-columns: 1fr;
  gap: clamp(1rem, 4vw, 2rem);
  display: grid;
}

@media (min-width: 640px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.modal-responsive {
  border-radius: clamp(.75rem, 2vw, 1rem);
  width: min(95vw, 500px);
  max-height: 90vh;
  margin: clamp(.5rem, 2vw, 1rem);
  overflow-y: auto;
}

.modal-responsive-wide {
  border-radius: clamp(.75rem, 2vw, 1rem);
  width: min(95vw, 800px);
  max-height: 90vh;
  margin: clamp(.5rem, 2vw, 1rem);
  overflow-y: auto;
}

.img-responsive {
  width: 100%;
  max-width: 100%;
  height: auto;
}

.img-responsive-square {
  object-fit: cover;
  width: min(200px, 80vw);
  height: min(200px, 80vw);
}

.flex-responsive-col-row {
  flex-direction: column;
  gap: clamp(.5rem, 2vw, 1rem);
  display: flex;
}

@media (min-width: 640px) {
  .flex-responsive-col-row {
    flex-direction: row;
    align-items: center;
  }
}

.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }
}

.show-mobile {
  display: block;
}

@media (min-width: 640px) {
  .show-mobile {
    display: none;
  }
}

@media (max-width: 360px) {
  .container {
    padding-left: .75rem;
    padding-right: .75rem;
  }

  .text-responsive-xs {
    font-size: .75rem;
  }

  .text-responsive-sm {
    font-size: .8125rem;
  }

  .btn-responsive {
    padding: .5rem .875rem;
    font-size: .8125rem;
  }

  .input-responsive {
    padding: .5rem .75rem;
    font-size: .8125rem;
  }
}

@media (min-width: 475px) {
  .xs\:inline {
    display: inline !important;
  }

  .xs\:hidden {
    display: none !important;
  }

  .xs\:flex-row {
    flex-direction: row !important;
  }

  .xs\:w-auto {
    width: auto !important;
  }

  .xs\:text-sm {
    font-size: .875rem !important;
  }

  .xs\:text-base {
    font-size: 1rem !important;
  }

  .xs\:text-lg {
    font-size: 1.125rem !important;
  }

  .xs\:px-3 {
    padding-left: .75rem !important;
    padding-right: .75rem !important;
  }

  .xs\:py-2 {
    padding-top: .5rem !important;
    padding-bottom: .5rem !important;
  }
}

.xs\:hidden, .xs\:inline {
  display: none;
}

@media (min-width: 475px) {
  .xs\:hidden {
    display: none !important;
  }

  .xs\:inline {
    display: inline !important;
  }
}

@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }

  .text-responsive-4xl {
    font-size: 3.5rem;
  }

  .spacing-responsive-xl {
    padding: 4rem;
  }
}

.btn-group-responsive {
  flex-direction: column;
  gap: .75rem;
  width: 100%;
  max-width: 20rem;
  margin: 0 auto;
  display: flex;
}

@media (min-width: 475px) {
  .btn-group-responsive {
    flex-direction: row;
    gap: 1rem;
    max-width: 32rem;
  }
}

@media (min-width: 1024px) {
  .btn-group-responsive {
    gap: 1.25rem;
    max-width: 48rem;
  }
}

.tutorials-modal .swal2-html-container {
  margin: 0 !important;
  padding: 0 !important;
}

@media (hover: none) and (pointer: coarse) {
  button, .btn, [role="button"] {
    min-width: 48px !important;
    min-height: 48px !important;
    padding: .875rem 1rem !important;
  }

  .card {
    padding: 1.25rem !important;
  }

  body {
    font-size: 16px !important;
  }

  .badge {
    padding: .5rem .75rem !important;
    font-size: .875rem !important;
  }
}

@media (max-height: 500px) and (orientation: landscape) {
  .hero-section {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .modal-responsive {
    max-height: 85vh !important;
    margin: .5rem !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  .grid-responsive-tablet {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
  }

  .text-tablet-lg {
    font-size: 1.375rem !important;
  }
}

@media (max-width: 474px) {
  .xs\:hidden {
    display: none !important;
  }

  .xs\:text-xs {
    font-size: .75rem !important;
  }

  .xs\:text-sm {
    font-size: .875rem !important;
  }

  .xs\:px-2 {
    padding-left: .5rem !important;
    padding-right: .5rem !important;
  }

  .xs\:py-1 {
    padding-top: .25rem !important;
    padding-bottom: .25rem !important;
  }

  .xs\:btn-compact {
    min-height: 40px !important;
    padding: .5rem .75rem !important;
    font-size: .8125rem !important;
  }
}

@media (min-width: 475px) {
  .xs\:inline {
    display: inline !important;
  }

  .xs\:block {
    display: block !important;
  }

  .xs\:flex {
    display: flex !important;
  }

  .xs\:flex-row {
    flex-direction: row !important;
  }

  .xs\:w-auto {
    width: auto !important;
  }
}

.btn-group-mobile {
  flex-direction: column;
  gap: .5rem;
  width: 100%;
  display: flex;
}

@media (min-width: 475px) {
  .btn-group-mobile {
    flex-direction: row;
    gap: .75rem;
  }
}

@media (min-width: 640px) {
  .btn-group-mobile {
    gap: 1rem;
  }
}

.card-mobile {
  border-radius: .75rem;
  margin: .5rem;
  padding: 1rem;
}

@media (min-width: 640px) {
  .card-mobile {
    border-radius: 1rem;
    margin: 0;
    padding: 1.5rem;
  }
}

.modal-mobile {
  border-radius: .75rem !important;
  width: calc(100vw - 1rem) !important;
  max-width: none !important;
  margin: .5rem !important;
}

@media (min-width: 640px) {
  .modal-mobile {
    border-radius: 1rem !important;
    width: min(90vw, 600px) !important;
    max-width: 600px !important;
    margin: 1rem !important;
  }
}

.text-mobile-responsive {
  font-size: clamp(.875rem, 3vw, 1rem);
  line-height: 1.5;
}

.text-mobile-responsive-lg {
  font-size: clamp(1rem, 4vw, 1.25rem);
  line-height: 1.4;
}

.text-mobile-responsive-xl {
  font-size: clamp(1.25rem, 5vw, 1.5rem);
  line-height: 1.3;
}

.badge-mobile {
  border-radius: .375rem;
  padding: .25rem .5rem;
  font-size: .75rem;
}

@media (min-width: 640px) {
  .badge-mobile {
    border-radius: .5rem;
    padding: .375rem .75rem;
    font-size: .875rem;
  }
}

.input-mobile {
  border-radius: .5rem !important;
  min-height: 48px !important;
  padding: .75rem !important;
  font-size: 16px !important;
}

.grid-mobile-responsive {
  grid-template-columns: 1fr;
  gap: 1rem;
  display: grid;
}

@media (min-width: 640px) {
  .grid-mobile-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-mobile-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.nav-mobile {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: #000c;
  padding: .75rem 1rem;
}

@media (min-width: 768px) {
  .nav-mobile {
    padding: 1rem 1.5rem;
  }
}

.hero-mobile {
  text-align: center;
  padding: 2rem 1rem;
}

@media (min-width: 640px) {
  .hero-mobile {
    padding: 3rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hero-mobile {
    padding: 4rem 2rem;
  }
}

.footer-mobile {
  text-align: center;
  padding: 1.5rem 1rem;
}

@media (min-width: 640px) {
  .footer-mobile {
    padding: 2rem 1.5rem;
  }
}

@media (hover: none) and (pointer: coarse) {
  button, .btn, [role="button"], a {
    touch-action: manipulation;
    min-width: 44px !important;
    min-height: 44px !important;
  }

  .touch-spacing {
    padding: 1rem !important;
  }

  body {
    font-size: 16px !important;
  }
}

@media (max-height: 500px) and (orientation: landscape) {
  .landscape-compact {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .landscape-hide {
    display: none !important;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }
}

@media (prefers-contrast: high) {
  .high-contrast {
    color: #000 !important;
    background: #fff !important;
    border: 2px solid !important;
  }
}

@media (prefers-color-scheme: dark) {
  .dark-mode-text {
    color: #f3f4f6 !important;
  }

  .dark-mode-bg {
    background-color: #1f2937 !important;
  }
}

.swal2-container, .swal2-container *, .swal2-popup, .swal2-popup *, .swal2-html-container, .swal2-content, .swal2-modal {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  overflow: hidden !important;
}

.swal2-container::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal2-container ::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal2-popup::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal2-popup ::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal2-html-container::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal2-content::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal2-modal::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.swal-clean-theme {
  border-radius: 15px !important;
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif !important;
  overflow: hidden !important;
  box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a !important;
}

.swal-clean-title {
  color: #1f2937 !important;
  margin-bottom: 15px !important;
  font-size: 20px !important;
  font-weight: 600 !important;
}

.swal-clean-content {
  color: #4b5563 !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  overflow: hidden !important;
}

.swal2-popup {
  overflow: hidden !important;
}

.test-result-modal .test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.test-result-content::-webkit-scrollbar {
  display: none !important;
}

.test-result-modal .swal2-html-container::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.test-result-modal .test-result-content::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.test-result-modal .swal2-html-container, .test-result-modal .test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.swal2-html-container {
  max-height: none !important;
  overflow: hidden !important;
}

.swal2-content, .swal2-modal {
  overflow: hidden !important;
}

.swal-clean-button {
  color: #fff !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all .2s !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.swal-clean-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 8px -1px #00000026 !important;
}

.swal-clean-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px #667eea4d !important;
}

.swal-clean-cancel-button {
  color: #fff !important;
  background: #6b7280 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all .2s !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.swal-clean-cancel-button:hover {
  background: #4b5563 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 8px -1px #00000026 !important;
}

.loading-spinner {
  border: 4px solid #f3f4f6;
  border-top-color: #667eea;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  animation: 1s linear infinite spin;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.swal2-loader {
  display: none !important;
}

.swal2-input {
  background: #fff !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: border-color .2s !important;
}

.swal2-input:focus {
  border-color: #667eea !important;
  outline: none !important;
  box-shadow: 0 0 0 3px #667eea1a !important;
}

.swal2-input::placeholder {
  color: #9ca3af !important;
}

.swal2-validation-message {
  color: #dc2626 !important;
  background: #fef2f2 !important;
  border: 1px solid #fecaca !important;
  border-radius: 6px !important;
  margin-top: 8px !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
}

@media (max-width: 480px) {
  .swal-clean-theme {
    width: calc(100% - 16px) !important;
    max-width: none !important;
    min-height: auto !important;
    margin: 8px !important;
  }

  .swal-clean-title {
    margin-bottom: 12px !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
  }

  .swal-clean-content {
    padding: 0 8px !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
  }

  .swal-clean-button, .swal-clean-cancel-button {
    min-width: 80px !important;
    padding: 8px 16px !important;
    font-size: 12px !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
  }

  .swal2-actions button {
    width: 100% !important;
    margin: 0 !important;
  }
}

@media (max-width: 640px) and (min-width: 481px) {
  .swal-clean-theme {
    width: calc(100% - 24px) !important;
    margin: 12px !important;
  }

  .swal-clean-title {
    font-size: 18px !important;
  }

  .swal-clean-button, .swal-clean-cancel-button {
    padding: 10px 20px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 768px) and (min-width: 641px) {
  .swal-clean-theme {
    width: calc(100% - 32px) !important;
    max-width: 500px !important;
    margin: 16px !important;
  }
}

.swal-success .swal-clean-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.swal-success .swal-clean-button:focus {
  box-shadow: 0 0 0 3px #10b9814d !important;
}

.swal-error .swal-clean-button {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.swal-error .swal-clean-button:focus {
  box-shadow: 0 0 0 3px #ef44444d !important;
}

.swal-info .swal-clean-button {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

.swal-info .swal-clean-button:focus {
  box-shadow: 0 0 0 3px #06b6d44d !important;
}

.swal-warning .swal-clean-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.swal-warning .swal-clean-button:focus {
  box-shadow: 0 0 0 3px #f59e0b4d !important;
}

.swal-confirm .swal-clean-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.swal-confirm .swal-clean-button:focus {
  box-shadow: 0 0 0 3px #3b82f64d !important;
}

.tutorials-modal {
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif !important;
}

.tutorials-modal .swal2-html-container {
  scrollbar-width: thin !important;
  scrollbar-color: #9b9b9b80 transparent !important;
  max-height: 70vh !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-y: auto !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar {
  width: 6px !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar-track {
  background: none !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar-thumb {
  background-color: #9b9b9b80 !important;
  border: #0000 !important;
  border-radius: 20px !important;
}

.tutorials-modal .swal2-popup {
  flex-direction: column !important;
  max-height: 90vh !important;
  display: flex !important;
}

@media (max-width: 480px) {
  .swal2-popup {
    width: 95vw !important;
    max-width: 95vw !important;
    max-height: 85vh !important;
    margin: 10px !important;
    padding: 15px !important;
  }

  .swal2-title {
    margin-bottom: 15px !important;
    font-size: 18px !important;
    line-height: 1.3 !important;
  }

  .swal2-html-container {
    max-height: 60vh !important;
    padding: 10px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    overflow-y: auto !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 10px !important;
    width: 100% !important;
  }

  .swal2-confirm, .swal2-cancel {
    width: 100% !important;
    margin: 0 !important;
    padding: 12px !important;
    font-size: 16px !important;
  }

  .test-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .test-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 15px !important;
  }

  .tutorials-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .tutorials-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 10px !important;
  }

  .pix-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .pix-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 15px !important;
  }
}

.tutorials-modal .tab-button {
  border: none !important;
  outline: none !important;
  font-family: inherit !important;
  transition: all .3s !important;
}

.tutorials-modal .tab-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px #0000001a !important;
}

.tutorials-modal .tab-content {
  animation: .3s ease-in-out fadeIn !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tutorials-modal .brand-item {
  border-left: 3px solid #059669 !important;
  margin-bottom: 12px !important;
  padding-left: 8px !important;
}

.tutorials-modal .compatibility-badge {
  color: #fff !important;
  background: #10b981 !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  display: inline-block !important;
}

:root {
  --radius: .625rem;
  --background: oklch(100% 0 0);
  --foreground: oklch(14.5% 0 0);
  --card: oklch(100% 0 0);
  --card-foreground: oklch(14.5% 0 0);
  --popover: oklch(100% 0 0);
  --popover-foreground: oklch(14.5% 0 0);
  --primary: oklch(20.5% 0 0);
  --primary-foreground: oklch(98.5% 0 0);
  --secondary: oklch(97% 0 0);
  --secondary-foreground: oklch(20.5% 0 0);
  --muted: oklch(97% 0 0);
  --muted-foreground: oklch(55.6% 0 0);
  --accent: oklch(97% 0 0);
  --accent-foreground: oklch(20.5% 0 0);
  --destructive: oklch(57.7% .245 27.325);
  --border: oklch(92.2% 0 0);
  --input: oklch(92.2% 0 0);
  --ring: oklch(70.8% 0 0);
  --chart-1: oklch(64.6% .222 41.116);
  --chart-2: oklch(60% .118 184.704);
  --chart-3: oklch(39.8% .07 227.392);
  --chart-4: oklch(82.8% .189 84.429);
  --chart-5: oklch(76.9% .188 70.08);
  --sidebar: oklch(98.5% 0 0);
  --sidebar-foreground: oklch(14.5% 0 0);
  --sidebar-primary: oklch(20.5% 0 0);
  --sidebar-primary-foreground: oklch(98.5% 0 0);
  --sidebar-accent: oklch(97% 0 0);
  --sidebar-accent-foreground: oklch(20.5% 0 0);
  --sidebar-border: oklch(92.2% 0 0);
  --sidebar-ring: oklch(70.8% 0 0);
}

.dark {
  --background: oklch(14.5% 0 0);
  --foreground: oklch(98.5% 0 0);
  --card: oklch(20.5% 0 0);
  --card-foreground: oklch(98.5% 0 0);
  --popover: oklch(26.9% 0 0);
  --popover-foreground: oklch(98.5% 0 0);
  --primary: oklch(92.2% 0 0);
  --primary-foreground: oklch(20.5% 0 0);
  --secondary: oklch(26.9% 0 0);
  --secondary-foreground: oklch(98.5% 0 0);
  --muted: oklch(26.9% 0 0);
  --muted-foreground: oklch(70.8% 0 0);
  --accent: oklch(37.1% 0 0);
  --accent-foreground: oklch(98.5% 0 0);
  --destructive: oklch(70.4% .191 22.216);
  --border: oklch(100% 0 0 / .1);
  --input: oklch(100% 0 0 / .15);
  --ring: oklch(55.6% 0 0);
  --chart-1: oklch(48.8% .243 264.376);
  --chart-2: oklch(69.6% .17 162.48);
  --chart-3: oklch(76.9% .188 70.08);
  --chart-4: oklch(62.7% .265 303.9);
  --chart-5: oklch(64.5% .246 16.439);
  --sidebar: oklch(20.5% 0 0);
  --sidebar-foreground: oklch(98.5% 0 0);
  --sidebar-primary: oklch(48.8% .243 264.376);
  --sidebar-primary-foreground: oklch(98.5% 0 0);
  --sidebar-accent: oklch(26.9% 0 0);
  --sidebar-accent-foreground: oklch(98.5% 0 0);
  --sidebar-border: oklch(100% 0 0 / .1);
  --sidebar-ring: oklch(43.9% 0 0);
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-pan-x {
  syntax: "*";
  inherits: false
}

@property --tw-pan-y {
  syntax: "*";
  inherits: false
}

@property --tw-pinch-zoom {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
/* SweetAlert2 Custom Styles */
.swal-dark-theme {
  background: #1e293b !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.75rem !important;
}

.swal-wide {
  max-width: 90vw !important;
  max-height: 90vh !important;
}

/* Responsividade para mobile */
@media (max-width: 480px) {
  .swal-wide {
    max-width: 98vw !important;
    max-height: 90vh !important;
    margin: 4px !important;
  }

  .swal-dark-theme {
    margin: 4px !important;
    width: calc(100% - 8px) !important;
    border-radius: 12px !important;
  }

  .swal-title {
    font-size: 1.125rem !important;
    line-height: 1.3 !important;
    padding: 8px !important;
  }

  .swal-text {
    font-size: 0.8125rem !important;
    line-height: 1.4 !important;
  }

  .swal-html-container {
    font-size: 0.8125rem !important;
    max-height: 65vh !important;
    padding: 4px !important;
    overflow-y: auto !important;
  }

  .swal-button {
    padding: 0.625rem 1rem !important;
    font-size: 0.8125rem !important;
    width: 100% !important;
    margin: 4px 0 !important;
    border-radius: 8px !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
    padding: 8px !important;
  }

  /* Melhorar QR Code em mobile */
  .swal-html-container img {
    max-width: min(160px, 70vw) !important;
    max-height: min(160px, 70vw) !important;
  }

  /* Melhorar textarea em mobile */
  .swal-html-container textarea {
    font-size: 0.75rem !important;
    height: 60px !important;
    padding: 8px !important;
  }
}

@media (max-width: 640px) and (min-width: 481px) {
  .swal-dark-theme {
    margin: 8px !important;
    width: calc(100% - 16px) !important;
  }

  .swal-title {
    font-size: 1.25rem !important;
  }

  .swal-button {
    padding: 0.75rem 1.25rem !important;
    font-size: 0.875rem !important;
  }

  .swal-html-container {
    max-height: 70vh !important;
    padding: 8px !important;
  }

  /* QR Code para tablets pequenos */
  .swal-html-container img {
    max-width: min(180px, 75vw) !important;
    max-height: min(180px, 75vw) !important;
  }
}

@media (max-width: 768px) and (min-width: 641px) {
  .swal-dark-theme {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-width: 500px !important;
  }
}

.swal-title {
  color: #ffffff !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
}

.swal-text {
  color: #d1d5db !important;
  font-size: 1rem !important;
}

.swal-html-container {
  color: #d1d5db !important;
  font-size: 1rem !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

.swal-html-container pre {
  background: #0f172a !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.5rem !important;
  padding: 1rem !important;
  margin: 0.5rem 0 !important;
  color: #e2e8f0 !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-x: auto !important;
}

.swal-html-container .emoji {
  font-size: 1.2em !important;
  margin-right: 0.5rem !important;
}

.swal-html-container .highlight {
  background: rgba(168, 85, 247, 0.2) !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  color: #c084fc !important;
  font-weight: 600 !important;
}

.swal-html-container .section-title {
  color: #a855f7 !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  margin: 1rem 0 0.5rem 0 !important;
  border-bottom: 1px solid rgba(168, 85, 247, 0.3) !important;
  padding-bottom: 0.25rem !important;
}

.swal-html-container .info-line {
  margin: 0.25rem 0 !important;
  padding: 0.25rem 0 !important;
}

.swal-html-container .link {
  color: #60a5fa !important;
  text-decoration: underline !important;
  word-break: break-all !important;
}

.swal-html-container .link:hover {
  color: #93c5fd !important;
}

.swal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  background: rgba(15, 23, 42, 0.5) !important;
}

.swal-button {
  background: linear-gradient(135deg, #a855f7, #ec4899) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  margin: 0 0.25rem !important;
  transition: all 0.3s ease !important;
}

.swal-button:hover {
  background: linear-gradient(135deg, #9333ea, #db2777) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.4) !important;
}

.swal-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.5) !important;
}

.swal-button.swal-button--cancel {
  background: #64748b !important;
}

.swal-button.swal-button--cancel:hover {
  background: #475569 !important;
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.4) !important;
}

.swal-input {
  background: #0f172a !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.5rem !important;
  color: #e2e8f0 !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
}

.swal-input:focus {
  border-color: #a855f7 !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2) !important;
  outline: none !important;
}

.swal-input::placeholder {
  color: #64748b !important;
}

.swal-select {
  background: #0f172a !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.5rem !important;
  color: #e2e8f0 !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
}

.swal-select:focus {
  border-color: #a855f7 !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2) !important;
  outline: none !important;
}

.swal-select option {
  background: #0f172a !important;
  color: #e2e8f0 !important;
}

.swal-textarea {
  background: #0f172a !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.5rem !important;
  color: #e2e8f0 !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
  resize: vertical !important;
  min-height: 100px !important;
}

.swal-textarea:focus {
  border-color: #a855f7 !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2) !important;
  outline: none !important;
}

.swal-textarea::placeholder {
  color: #64748b !important;
}

.swal-loading {
  border-color: #a855f7 !important;
}

.swal-loading:after {
  border-color: #a855f7 transparent #a855f7 transparent !important;
}

.swal-icon {
  border-color: #a855f7 !important;
}

.swal-icon.swal-icon--success {
  border-color: #10b981 !important;
}

.swal-icon.swal-icon--error {
  border-color: #ef4444 !important;
}

.swal-icon.swal-icon--warning {
  border-color: #f59e0b !important;
}

.swal-icon.swal-icon--info {
  border-color: #3b82f6 !important;
}

.swal-icon.swal-icon--question {
  border-color: #8b5cf6 !important;
}

/* Scrollbar customization for webkit browsers */
.swal-html-container::-webkit-scrollbar {
  width: 8px !important;
}

.swal-html-container::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5) !important;
  border-radius: 4px !important;
}

.swal-html-container::-webkit-scrollbar-thumb {
  background: rgba(168, 85, 247, 0.5) !important;
  border-radius: 4px !important;
}

.swal-html-container::-webkit-scrollbar-thumb:hover {
  background: rgba(168, 85, 247, 0.7) !important;
}

/* Animation improvements */
.swal2-show {
  animation: swal2-show 0.3s ease-out !important;
}

.swal2-hide {
  animation: swal2-hide 0.15s ease-in !important;
}

@keyframes swal2-show {
  0% {
    transform: scale(0.7) !important;
    opacity: 0 !important;
  }
  45% {
    transform: scale(1.05) !important;
    opacity: 1 !important;
  }
  80% {
    transform: scale(0.95) !important;
  }
  100% {
    transform: scale(1) !important;
    opacity: 1 !important;
  }
}

@keyframes swal2-hide {
  0% {
    transform: scale(1) !important;
    opacity: 1 !important;
  }
  100% {
    transform: scale(0.5) !important;
    opacity: 0 !important;
  }
}
/* SweetAlert2 Clean Theme - Padrão PIX */

/* Tema limpo com fundo branco */
.swal-clean-theme {
  border-radius: 16px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  background: #ffffff !important;
  color: #333333 !important;
}

.swal-clean-title {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #333333 !important;
  margin-bottom: 16px !important;
}

.swal-clean-content {
  padding: 0 !important;
  color: #333333 !important;
}

/* Botões personalizados */
.swal2-confirm {
  border-radius: 8px !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
}

.swal2-cancel {
  border-radius: 8px !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
}

/* Toast notifications */
.swal2-toast.swal-clean-theme {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* Inputs nos alertas */
.swal2-input {
  border: 2px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 12px !important;
  font-size: 14px !important;
  background: #f9f9f9 !important;
  color: #333333 !important;
}

.swal2-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.swal2-select {
  border: 2px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 12px !important;
  font-size: 14px !important;
  background: #f9f9f9 !important;
  color: #333333 !important;
}

.swal2-select:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.swal2-textarea {
  border: 2px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 12px !important;
  font-size: 12px !important;
  background: #f9f9f9 !important;
  color: #333333 !important;
  font-family: 'JetBrains Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !important;
}

/* Loading spinner */
.swal2-loading .swal2-styled.swal2-confirm {
  background-color: #667eea !important;
}

/* Validation message */
.swal2-validation-message {
  background: #fee2e2 !important;
  color: #dc2626 !important;
  border: 1px solid #fecaca !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
}

/* Header gradients personalizados */
.gradient-header-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  padding: 16px !important;
  border-radius: 12px !important;
  margin-bottom: 20px !important;
}

.gradient-header-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: white !important;
  padding: 16px !important;
  border-radius: 12px !important;
  margin-bottom: 20px !important;
}

.gradient-header-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 16px !important;
  border-radius: 12px !important;
  margin-bottom: 20px !important;
}

.gradient-header-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
  padding: 16px !important;
  border-radius: 12px !important;
  margin-bottom: 20px !important;
}

/* Responsividade aprimorada */
@media (max-width: 480px) {
  .swal-clean-theme {
    margin: 8px !important;
    width: calc(100% - 16px) !important;
    max-width: none !important;
    border-radius: 12px !important;
  }

  .swal-clean-title {
    font-size: 18px !important;
    line-height: 1.3 !important;
    margin-bottom: 12px !important;
  }

  .swal-clean-content {
    font-size: 13px !important;
    line-height: 1.5 !important;
  }

  .swal2-confirm, .swal2-cancel {
    padding: 8px 16px !important;
    font-size: 12px !important;
    width: 100% !important;
    margin: 4px 0 !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
    margin-top: 16px !important;
  }

  .swal2-input, .swal2-select {
    font-size: 14px !important;
    padding: 10px !important;
  }

  .pix-popup {
    border-radius: 12px !important;
  }

  .pix-title {
    font-size: 18px !important;
  }
}

@media (max-width: 640px) and (min-width: 481px) {
  .swal-clean-theme {
    margin: 12px !important;
    width: calc(100% - 24px) !important;
  }

  .swal-clean-title {
    font-size: 20px !important;
  }

  .swal2-confirm, .swal2-cancel {
    padding: 10px 20px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 768px) and (min-width: 641px) {
  .swal-clean-theme {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-width: 500px !important;
  }

  .swal-clean-title {
    font-size: 22px !important;
  }

  .swal2-confirm, .swal2-cancel {
    padding: 11px 22px !important;
    font-size: 14px !important;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .swal-clean-theme {
    max-width: 600px !important;
  }
}

/* PIX específico */
.pix-popup {
  border-radius: 16px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  background: #ffffff !important;
}

.pix-title {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #333333 !important;
}

.pix-content {
  padding: 0 !important;
}

/* Animações suaves */
.swal2-show {
  animation: swal2-show 0.3s ease-out !important;
}

.swal2-hide {
  animation: swal2-hide 0.15s ease-in !important;
}

@keyframes swal2-show {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }
  45% {
    transform: scale(1.05);
    opacity: 1;
  }
  80% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes swal2-hide {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.5);
    opacity: 0;
  }
}
/* Responsividade geral para toda a aplicação */

/* Containers responsivos */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Tipografia responsiva */
.text-responsive-xs {
  font-size: clamp(0.75rem, 2vw, 0.875rem);
}

.text-responsive-sm {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 3vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 3.5vw, 1.25rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 5vw, 2rem);
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 6vw, 2.5rem);
}

.text-responsive-4xl {
  font-size: clamp(2.25rem, 7vw, 3rem);
}

/* Espaçamentos responsivos */
.spacing-responsive-sm {
  padding: clamp(0.5rem, 2vw, 1rem);
}

.spacing-responsive-md {
  padding: clamp(1rem, 3vw, 1.5rem);
}

.spacing-responsive-lg {
  padding: clamp(1.5rem, 4vw, 2rem);
}

.spacing-responsive-xl {
  padding: clamp(2rem, 5vw, 3rem);
}

/* Cards responsivos */
.card-responsive {
  border-radius: clamp(0.5rem, 2vw, 1rem);
  padding: clamp(1rem, 4vw, 2rem);
}

/* Botões responsivos */
.btn-responsive {
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 4vw, 1.5rem);
  font-size: clamp(0.875rem, 3vw, 1rem);
  border-radius: clamp(0.375rem, 1.5vw, 0.5rem);
}

.btn-responsive-lg {
  padding: clamp(0.75rem, 3vw, 1rem) clamp(1.5rem, 5vw, 2rem);
  font-size: clamp(1rem, 3.5vw, 1.125rem);
  border-radius: clamp(0.5rem, 2vw, 0.75rem);
}

/* Inputs responsivos */
.input-responsive {
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(0.75rem, 3vw, 1rem);
  font-size: clamp(0.875rem, 3vw, 1rem);
  border-radius: clamp(0.375rem, 1.5vw, 0.5rem);
}

/* Grids responsivos */
.grid-responsive-1-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: clamp(1rem, 4vw, 2rem);
}

@media (min-width: 768px) {
  .grid-responsive-1-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.grid-responsive-1-2-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: clamp(1rem, 4vw, 2rem);
}

@media (min-width: 640px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Modais responsivos */
.modal-responsive {
  width: min(95vw, 500px);
  max-height: 90vh;
  margin: clamp(0.5rem, 2vw, 1rem);
  border-radius: clamp(0.75rem, 2vw, 1rem);
  overflow-y: auto;
}

.modal-responsive-wide {
  width: min(95vw, 800px);
  max-height: 90vh;
  margin: clamp(0.5rem, 2vw, 1rem);
  border-radius: clamp(0.75rem, 2vw, 1rem);
  overflow-y: auto;
}

/* Imagens responsivas */
.img-responsive {
  width: 100%;
  height: auto;
  max-width: 100%;
}

.img-responsive-square {
  width: min(200px, 80vw);
  height: min(200px, 80vw);
  object-fit: cover;
}

/* Flexbox responsivo */
.flex-responsive-col-row {
  display: flex;
  flex-direction: column;
  gap: clamp(0.5rem, 2vw, 1rem);
}

@media (min-width: 640px) {
  .flex-responsive-col-row {
    flex-direction: row;
    align-items: center;
  }
}

/* Utilitários de visibilidade */
.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }
}

.show-mobile {
  display: block;
}

@media (min-width: 640px) {
  .show-mobile {
    display: none;
  }
}

/* Ajustes específicos para telas muito pequenas */
@media (max-width: 360px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .text-responsive-xs {
    font-size: 0.75rem;
  }

  .text-responsive-sm {
    font-size: 0.8125rem;
  }

  .btn-responsive {
    padding: 0.5rem 0.875rem;
    font-size: 0.8125rem;
  }

  .input-responsive {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Breakpoint personalizado para xs (extra small) */
@media (min-width: 475px) {
  .xs\:inline {
    display: inline !important;
  }

  .xs\:hidden {
    display: none !important;
  }

  .xs\:flex-row {
    flex-direction: row !important;
  }

  .xs\:w-auto {
    width: auto !important;
  }

  .xs\:text-sm {
    font-size: 0.875rem !important;
  }

  .xs\:text-base {
    font-size: 1rem !important;
  }

  .xs\:text-lg {
    font-size: 1.125rem !important;
  }

  .xs\:px-3 {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  .xs\:py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* Utilitários para xs breakpoint quando não está disponível */
.xs\:hidden {
  display: none;
}

.xs\:inline {
  display: none;
}

@media (min-width: 475px) {
  .xs\:hidden {
    display: none !important;
  }

  .xs\:inline {
    display: inline !important;
  }
}

/* Ajustes para telas grandes */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }

  .text-responsive-4xl {
    font-size: 3.5rem;
  }

  .spacing-responsive-xl {
    padding: 4rem;
  }
}

/* Estilos específicos para botões responsivos */
.btn-group-responsive {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  max-width: 20rem;
  margin: 0 auto;
}

@media (min-width: 475px) {
  .btn-group-responsive {
    flex-direction: row;
    max-width: 32rem;
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .btn-group-responsive {
    max-width: 48rem;
    gap: 1.25rem;
  }
}

/* Estilos para modal de tutoriais */
.tutorials-modal {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.tutorials-modal .swal2-html-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* Melhorias para touch devices */
@media (hover: none) and (pointer: coarse) {
  /* Aumentar área de toque em dispositivos móveis */
  button, .btn, [role="button"] {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 0.875rem 1rem !important;
  }

  /* Melhorar espaçamento em cards */
  .card {
    padding: 1.25rem !important;
  }

  /* Aumentar fonte em dispositivos touch */
  body {
    font-size: 16px !important;
  }

  /* Melhorar badges em touch */
  .badge {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* Otimizações para landscape em mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .hero-section {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .modal-responsive {
    max-height: 85vh !important;
    margin: 0.5rem !important;
  }
}

/* Melhorias para tablets */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  .grid-responsive-tablet {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
  }

  .text-tablet-lg {
    font-size: 1.375rem !important;
  }
}
