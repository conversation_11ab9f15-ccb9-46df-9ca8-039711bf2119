{"name": "smartv-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host localhost --port 5173", "build": "vite build", "preview": "vite preview", "start": "concurrently \"npm run dev\" \"cd api && node proxy-server.cjs\""}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.1", "lucide-react": "^0.468.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "date-fns": "^4.1.0", "sweetalert2": "^11.14.5"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^4.5.14", "concurrently": "^9.1.0"}}