/* tslint:disable */
/* eslint:disable */
// Automatically generated by gen_type_defs. Do not edit.

export interface ApiCallData {
  api: APIDesc
  method: string
  path: string
  pathAndQuery: string
  pathParams?: any
  parsedPayload?: any
  headers: Record<string, any>
}

export class ApiCallError {
  code: string
  message: string
  details?: PVals
}
export type APICallError = ApiCallError

export interface ApiDesc {
  service: string
  endpoint: string
  raw: boolean
  requiresAuth: boolean
  tags: Array<string>
}

export interface ApiRoute {
  service: string
  name: string
  raw: boolean
  streamingRequest: boolean
  streamingResponse: boolean
  handler: (...args: any[]) => any
}

export interface AppMeta {
  appId: string
  apiBaseUrl: string
  environment: EnvironmentMeta
  build: BuildMeta
  deploy: DeployMeta
}

export interface AttrsOptions {
  version?: string
}

export class BodyReader {
  start(push: (...args: any[]) => any, destroy: (...args: any[]) => any): void
  read(): void
}

export class Bucket {
  object(name: string): BucketObject
  list(options?: ListOptions | undefined | null, source?: Request | undefined | null): Promise<ListIterator | TypedObjectError>
}

export class BucketObject {
  attrs(options?: AttrsOptions | undefined | null, source?: Request | undefined | null): Promise<ObjectAttrs | TypedObjectError>
  exists(options?: ExistsOptions | undefined | null, source?: Request | undefined | null): Promise<boolean | TypedObjectError>
  upload(data: Buffer, opts?: UploadOptions | undefined | null, source?: Request | undefined | null): Promise<ObjectAttrs | TypedObjectError>
  signedUploadUrl(options?: UploadUrlOptions | undefined | null, source?: Request | undefined | null): Promise<SignedUploadUrl | TypedObjectError>
  signedDownloadUrl(options?: DownloadUrlOptions | undefined | null, source?: Request | undefined | null): Promise<SignedDownloadUrl | TypedObjectError>
  downloadAll(options?: DownloadOptions | undefined | null, source?: Request | undefined | null): Promise<Buffer | TypedObjectError>
  delete(options?: DeleteOptions | undefined | null, source?: Request | undefined | null): Promise<TypedObjectError | null>
  publicUrl(): string
}

export interface BuildMeta {
  revision: string
  uncommittedChanges: boolean
}

/** CallOpts can be used to set options for API calls. */
export interface CallOpts {
  authData?: PVals
}

export enum CloudProvider {
  AWS = 0,
  GCP = 1,
  Azure = 2,
  Encore = 3,
  Local = 4
}

export class Cursor {
  next(): Promise<Row | null>
}

export interface DeleteOptions {
  version?: string
}

export interface DeployMeta {
  id: string
  deployTime: string
  hostedServices: Record<string, HostedService>
}

export interface DownloadOptions {
  version?: string
}

export interface DownloadUrlOptions {
  ttl?: number
}

export interface EnvironmentMeta {
  name: string
  type: EnvironmentType
  cloud: CloudProvider
}

export enum EnvironmentType {
  Production = 0,
  Development = 1,
  Ephemeral = 2,
  Test = 3
}

export interface ExistsOptions {
  version?: string
}

export class Gateway {}

export interface GatewayConfig {
  auth?: (...args: any[]) => any
}

export interface HostedService {
  name: string
}

export class ListEntry {
  name: string
  size: number
  etag: string
}

export class ListIterator {
  next(): Promise<ListEntry | null>
  markDone(): void
}

export interface ListOptions {
  prefix?: string
  limit?: number
}

export enum LogLevel {
  Trace = 1,
  Debug = 2,
  Info = 3,
  Warn = 4,
  Error = 5
}

/** A logger that can be used to log messages from the runtime. */
export class Logger {
  /** log a message from the application */
  log(request: Request | undefined | null, level: LogLevel, msg: string, error?: Error, caller?: string, fields?: Record<string, unknown>): void
  /** Returns a new logger with the specified level */
  withLevel(level: LogLevel): Logger
  /**
   * Returns a new logger with the given fields added to the context
   * that the logger will use when emitting logs as extra fields
   */
  with(fields: Record<string, unknown>): Logger
}

export class ObjectAttrs {
  name: string
  version?: string
  size: number
  contentType?: string
  etag: string
}

export enum ObjectErrorKind {
  NotFound = 0,
  PreconditionFailed = 1,
  InvalidArgument = 2,
  Other = 3,
  Internal = 4
}

export interface PubSubMessageData {
  service: string
  topic: string
  subscription: string
  id: string
  publishedAt: string
  deliveryAttempt: number
  parsedPayload?: PVals
}

export class PubSubSubscription {
  subscribe(): Promise<void>
}

export interface PubSubSubscriptionConfig {
  topicName: string
  subscriptionName: string
  handler: (...args: any[]) => any
}

export class PubSubTopic {
  publish(body: unknown, source?: Request | undefined | null): Promise<string>
}

export class QueryArgs {
  constructor(params: Array<unknown>)
}

export class Request {
  payload(): unknown
  meta(): RequestMeta
  getAuthData(): unknown
}

export interface RequestMeta {
  startedAt: string
  trace?: TraceData
  apiCall?: APICallData
  pubsubMessage?: PubSubMessageData
}

export class ResponseWriter {
  writeHead(status: number, headers: Array<string> | Record<string, string | number | Array<string>>): void
  writeBody(buf: Buffer, callback?: (...args: any[]) => any | undefined | null): void
  writeBodyMulti(bufs: Array<Buffer>, callback?: (...args: any[]) => any | undefined | null): void
  close(buf?: Buffer | undefined | null, callback?: (...args: any[]) => any | undefined | null): void
}

export class Row {
  values(): Record<string, unknown>
}

export class Runtime {
  constructor(options?: RuntimeOptions | undefined | null)
  runForever(): Promise<void>
  sqlDatabase(encoreName: string): SQLDatabase
  pubsubTopic(encoreName: string): PubSubTopic
  bucket(encoreName: string): Bucket
  gateway(encoreName: string, cfg: GatewayConfig): Gateway
  /** Gets the root logger from the runtime */
  logger(): Logger
  pubsubSubscription(cfg: PubSubSubscriptionConfig): PubSubSubscription
  registerHandler(route: ApiRoute): void
  registerTestHandler(route: ApiRoute): void
  registerHandlers(routes: Array<ApiRoute>): void
  secret(encoreName: string): Secret | null
  apiCall(service: string, endpoint: string, payload?: unknown | undefined | null, source?: Request | undefined | null, opts?: CallOpts | undefined | null): Promise<Record<string, any> | null | ApiCallError>
  stream(service: string, endpoint: string, payload?: unknown | undefined | null, source?: Request | undefined | null, opts?: CallOpts | undefined | null): Promise<WebSocketClient>
  /** Returns the version of the Encore runtime being used */
  static version(): string
  /** Returns the git commit hash used to build the Encore runtime */
  static buildCommit(): string
  appMeta(): AppMeta
  /**
   * Reports the total number of worker threads,
   * including the main thread.
   */
  numWorkerThreads(): number
}

export interface RuntimeOptions {
  testMode?: boolean
}

export class Secret {
  /** Returns the cached value of the secret. */
  cached(): string
}

export interface SignedDownloadUrl {
  url: string
}

export interface SignedUploadUrl {
  url: string
}

export class Sink {
  send(msg: PVals): void
  close(): void
}

export class Socket {
  send(msg: PVals): void
  recv(): Promise<PVals>
  close(): void
}

export class SqlConn {
  close(): Promise<void>
  query(query: string, args: QueryArgs, source?: Request | undefined | null): Promise<Cursor>
  queryRow(query: string, args: QueryArgs, source?: Request | undefined | null): Promise<Row | null>
}
export type SQLConn = SqlConn

export class SqlDatabase {
  /** Reports the connection string to connect to this database. */
  connString(): string
  /** Begins a transaction */
  begin(source?: Request | undefined | null): Promise<Transaction>
  query(query: string, args: QueryArgs, source?: Request | undefined | null): Promise<Cursor>
  queryRow(query: string, args: QueryArgs, source?: Request | undefined | null): Promise<Row | null>
  acquire(): Promise<SQLConn>
}
export type SQLDatabase = SqlDatabase

export class Stream {
  recv(): Promise<PVals>
}

export interface TraceData {
  traceId: string
  spanId: string
  parentTraceId?: string
  parentSpanId?: string
  extCorrelationId?: string
}

export class Transaction {
  commit(source?: Request | undefined | null): Promise<void>
  rollback(source?: Request | undefined | null): Promise<void>
  query(query: string, args: QueryArgs, source?: Request | undefined | null): Promise<Cursor>
}

export class TypedObjectError {
  kind: ObjectErrorKind
  message: string
}

export interface UploadOptions {
  contentType?: string
  preconditions?: UploadPreconditions
}

export interface UploadPreconditions {
  notExists?: boolean
}

export interface UploadUrlOptions {
  ttl?: number
}

export class WebSocketClient {
  send(msg: unknown): void
  recv(): Promise<Record<string, any>>
  close(): void
}

