import { getCurrentRequest } from "./internal/reqtrack/mod.js";
/**
 * Returns information about the running Encore request,
 * such as API calls and Pub/Sub messages being processed.
 *
 * Returns undefined only if no request is being processed,
 * such as during system initialization.
 */
export function currentRequest() {
    const req = getCurrentRequest();
    if (!req) {
        return undefined;
    }
    const meta = req.meta();
    const base = {
        trace: meta.trace
    };
    if (meta.apiCall) {
        const api = {
            type: "api-call",
            api: {
                service: meta.apiCall.api.service,
                endpoint: meta.apiCall.api.endpoint,
                raw: meta.apiCall.api.raw,
                auth: meta.apiCall.api.requiresAuth,
                tags: meta.apiCall.api.tags
            },
            method: meta.apiCall.method,
            path: meta.apiCall.path,
            pathAndQuery: meta.apiCall.pathAndQuery,
            pathParams: meta.apiCall.pathParams ?? {},
            parsedPayload: meta.apiCall.parsedPayload,
            headers: meta.apiCall.headers,
            middlewareData: req.middlewareData
        };
        return { ...base, ...api };
    }
    else if (meta.pubsubMessage) {
        const msg = {
            type: "pubsub-message",
            service: meta.pubsubMessage.service,
            topic: meta.pubsubMessage.topic,
            subscription: meta.pubsubMessage.subscription,
            messageId: meta.pubsubMessage.id,
            deliveryAttempt: meta.pubsubMessage.deliveryAttempt,
            parsedPayload: meta.pubsubMessage.parsedPayload
        };
        return { ...base, ...msg };
    }
    else {
        return undefined;
    }
}
//# sourceMappingURL=req_meta.js.map