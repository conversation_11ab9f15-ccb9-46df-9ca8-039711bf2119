import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './'),
      '~backend': path.resolve(__dirname, './api')
    }
  },
  server: {
    host: 'localhost',
    port: 5173,
    allowedHosts: ['localhost', '127.0.0.1', 'smartv.shop']
  },
  preview: {
    host: 'localhost',
    port: 5173,
    allowedHosts: ['localhost', '127.0.0.1', 'smartv.shop']
  }
})
