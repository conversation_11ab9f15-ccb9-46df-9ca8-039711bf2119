import * as stream from "node:stream";
export class RawRequest extends stream.Readable {
    complete;
    trailers;
    trailersDistinct;
    rawTrailers;
    connection; // deprecated
    socket;
    body;
    req;
    constructor(req, body) {
        super({});
        this.req = req;
        this.complete = false;
        this.trailers = {};
        this.trailersDistinct = {};
        this.rawTrailers = [];
        this.body = body;
        this.body.start(this.push.bind(this), this.destroy.bind(this));
        // Set the socket to a dummy value for legacy compatibility with Express.js.
        this.socket = new DummySocket();
        this.connection = this.socket; // legacy alias
    }
    get method() {
        return this.meta.apiCall.method;
    }
    _url;
    get url() {
        if (!this._url) {
            this._url = this.meta.apiCall.pathAndQuery;
        }
        return this._url;
    }
    set url(value) {
        this._url = value;
    }
    get headers() {
        return this.meta.apiCall.headers;
    }
    _headersDistinct;
    get headersDistinct() {
        if (this._headersDistinct) {
            return this._headersDistinct;
        }
        const headers = {};
        for (const [key, value] of Object.entries(this.headers)) {
            if (value !== undefined) {
                const val = Array.isArray(value) ? value : [value];
                headers[key] = val;
            }
        }
        this._headersDistinct = headers;
        return headers;
    }
    _rawHeaders;
    get rawHeaders() {
        if (this._rawHeaders) {
            return this._rawHeaders;
        }
        this._rawHeaders = Object.keys(this.headers);
        return this._rawHeaders;
    }
    _meta;
    get meta() {
        if (this._meta === undefined) {
            this._meta = this.req.meta();
        }
        return this._meta;
    }
    _read(size) {
        this.body.read();
    }
    setTimeout(msecs, callback) {
        // Not yet implemented.
        return this;
    }
}
export class RawResponse extends stream.Writable {
    req;
    chunkedEncoding;
    shouldKeepAlive;
    // useChunkedEncodingByDefault: boolean;
    sendDate;
    statusCode;
    statusMessage;
    finished; // deprecated
    headersSent;
    strictContentLength;
    connection; // deprecated
    socket;
    w;
    headers;
    constructor(req, w) {
        super({ highWaterMark: 1024 * 1024 }); // TODO?
        this.req = req;
        this.chunkedEncoding = false; // TODO
        this.shouldKeepAlive = true;
        this.sendDate = true;
        this.statusCode = 200;
        this.statusMessage = undefined;
        this.finished = false;
        this.strictContentLength = false;
        this.headersSent = false;
        this.headers = {};
        this.w = w;
        // Set the socket to a dummy value for legacy compatibility with Express.js.
        this.socket = new DummySocket();
        this.connection = this.socket; // legacy alias
    }
    write(chunk, encoding, callback) {
        const res = super.write(chunk, encoding, callback);
        // HACK: Work around pipe deadlock in Node.js when writing a large response.
        return true;
    }
    // Needed for Next.js compatibility.
    _implicitHeader() {
        this._writeHeaderIfNeeded();
    }
    _writeHeaderIfNeeded() {
        if (!this.headersSent) {
            this.w.writeHead(this.statusCode, this.headers);
            this.headersSent = true;
        }
    }
    _write(chunk, _encoding, callback) {
        this._writeHeaderIfNeeded();
        this.w.writeBody(chunk, callback);
    }
    _writev(chunks, callback) {
        this._writeHeaderIfNeeded();
        this.w.writeBodyMulti(chunks.map((ch) => ch.chunk), callback);
    }
    _final(callback) {
        this._writeHeaderIfNeeded();
        this.w.close(undefined, callback);
    }
    setTimeout(msecs, callback) {
        // Not implemented yet.
        return this;
    }
    setHeader(name, value) {
        this.headers[name] = value;
        return this;
    }
    appendHeader(name, value) {
        const existing = this.headers[name];
        const existingIsArr = Array.isArray(existing);
        const valIsArr = Array.isArray(value);
        if (existingIsArr && valIsArr) {
            existing.push(...value);
        }
        else if (existingIsArr) {
            existing.push("" + value);
        }
        else if (existing !== undefined) {
            this.headers[name] = ["" + existing, "" + value];
        }
        else {
            this.headers[name] = value;
        }
        return this;
    }
    getHeader(name) {
        return this.headers[name];
    }
    getHeaders() {
        return this.headers;
    }
    getHeaderNames() {
        return Object.keys(this.headers);
    }
    hasHeader(name) {
        return this.headers[name] !== undefined;
    }
    removeHeader(name) {
        delete this.headers[name];
    }
    addTrailers(headers) {
        // Not implemented yet.
    }
    flushHeaders() {
        this._writeHeaderIfNeeded();
    }
    writeHead(statusCode, statusMessageOrHeaders, headers) {
        this.statusCode = statusCode;
        const headersIn = typeof statusMessageOrHeaders === "string"
            ? headers
            : statusMessageOrHeaders;
        // Merge headers, if provided.
        if (headersIn) {
            if (Array.isArray(headersIn)) {
                for (let i = 0; i < headersIn.length; i += 2) {
                    const key = headersIn[i];
                    const value = headersIn[i + 1];
                    if (typeof key === "string" && typeof value === "string") {
                        this.headers[key] = value;
                    }
                }
            }
            else {
                for (const key in headersIn) {
                    const value = headersIn[key];
                    this.headers[key] = value;
                }
            }
        }
        this._writeHeaderIfNeeded();
        return this;
    }
}
// DummySocket is a dummy implementation of the net.Socket class.
//
// It's provided because certain libraries like Express expect the `socket` attribute
// to be non-null on the request and response object.
class DummySocket extends stream.Duplex {
    destroySoon() { }
    write() { return true; }
    connect() { return this; }
    setEncoding(_encoding) { return this; }
    pause() { return this; }
    resetAndDestroy() { return this; }
    resume() { return this; }
    setTimeout(_timeout, _callback) { return this; }
    setNoDelay(_noDelay) { return this; }
    setKeepAlive(_enable, _initialDelay) { return this; }
    address() { return {}; }
    unref() { return this; }
    ref() { return this; }
    autoSelectFamilyAttemptedAddresses = [];
    bufferSize = 0;
    bytesRead = 0;
    bytesWritten = 0;
    connecting = false;
    pending = false;
    destroyed = false;
    localAddress = undefined;
    localPort = undefined;
    localFamily = undefined;
    readyState = 'open';
    remoteAddress = undefined;
    remoteFamily = undefined;
    remotePort = undefined;
    timeout = undefined;
    end() { return this; }
    addListener(_event, _listener) { return this; }
    emit(_event, ..._args) { return true; }
    on(_event, _listener) { return this; }
    once(_event, _listener) { return this; }
    prependListener(_event, _listener) { return this; }
    prependOnceListener(_event, _listener) { return this; }
}
//# sourceMappingURL=node_http.js.map